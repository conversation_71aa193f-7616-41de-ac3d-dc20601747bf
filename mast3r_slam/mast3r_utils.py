import os
import PIL
import numpy as np
import torch
import cv2
import einops
import copy

from mast3r.cloud_opt.sparse_ga import sparse_global_alignment
from mast3r.cloud_opt.tsdf_optimizer import TSDFPostProcess
from dust3r.utils.image import load_images
from dust3r.utils.device import to_numpy
from dust3r.image_pairs import make_pairs

import mast3r.utils.path_to_dust3r  # noqa
from dust3r.utils.image import ImgNorm
from mast3r.model import AsymmetricMASt3R
from mast3r_slam.retrieval_database import RetrievalDatabase
from mast3r_slam.config import config
import mast3r_slam.matching as matching


def load_mast3r(path=None, device="cuda"):
    weights_path = (
        "checkpoints/MASt3R_ViTLarge_BaseDecoder_512_catmlpdpt_metric.pth"
        if path is None
        else path
    )
    model = AsymmetricMASt3R.from_pretrained(weights_path).to(device)
    return model


def load_retriever(mast3r_model, retriever_path=None, device="cuda"):
    retriever_path = (
        "checkpoints/MASt3R_ViTLarge_BaseDecoder_512_catmlpdpt_metric_retrieval_trainingfree.pth"
        if retriever_path is None
        else retriever_path
    )
    retriever = RetrievalDatabase(retriever_path, backbone=mast3r_model, device=device)
    return retriever


@torch.inference_mode
def decoder(model, feat1, feat2, pos1, pos2, shape1, shape2):
    dec1, dec2 = model._decoder(feat1, pos1, feat2, pos2)
    with torch.amp.autocast(enabled=False, device_type="cuda"):
        res1 = model._downstream_head(1, [tok.float() for tok in dec1], shape1)
        res2 = model._downstream_head(2, [tok.float() for tok in dec2], shape2)
    return res1, res2


def downsample(X, C, D, Q):
    downsample = config["dataset"]["img_downsample"]
    if downsample > 1:
        # C and Q: (...xHxW)
        # X and D: (...xHxWxF)
        X = X[..., ::downsample, ::downsample, :].contiguous()
        C = C[..., ::downsample, ::downsample].contiguous()
        D = D[..., ::downsample, ::downsample, :].contiguous()
        Q = Q[..., ::downsample, ::downsample].contiguous()
    return X, C, D, Q


@torch.inference_mode
def mast3r_symmetric_inference(model, frame_i, frame_j):
    if frame_i.feat is None:
        frame_i.feat, frame_i.pos, _ = model._encode_image(
            frame_i.img, frame_i.img_true_shape
        )
    if frame_j.feat is None:
        frame_j.feat, frame_j.pos, _ = model._encode_image(
            frame_j.img, frame_j.img_true_shape
        )

    feat1, feat2 = frame_i.feat, frame_j.feat
    pos1, pos2 = frame_i.pos, frame_j.pos
    shape1, shape2 = frame_i.img_true_shape, frame_j.img_true_shape

    res11, res21 = decoder(model, feat1, feat2, pos1, pos2, shape1, shape2)
    res22, res12 = decoder(model, feat2, feat1, pos2, pos1, shape2, shape1)
    res = [res11, res21, res22, res12]
    print(res11.keys())
    X, C, D, Q = zip(
        *[(r["pts3d"][0], r["conf"][0], r["desc"][0], r["desc_conf"][0]) for r in res]
    )
    # 4xhxwxc
    X, C, D, Q = torch.stack(X), torch.stack(C), torch.stack(D), torch.stack(Q)
    X, C, D, Q = downsample(X, C, D, Q)
    return X, C, D, Q


# NOTE: Assumes img shape the same
@torch.inference_mode
def mast3r_decode_symmetric_batch(
    model, feat_i, pos_i, feat_j, pos_j, shape_i, shape_j
):
    B = feat_i.shape[0]
    X, C, D, Q = [], [], [], []
    for b in range(B):
        feat1 = feat_i[b][None]
        feat2 = feat_j[b][None]
        pos1 = pos_i[b][None]
        pos2 = pos_j[b][None]
        res11, res21 = decoder(model, feat1, feat2, pos1, pos2, shape_i[b], shape_j[b])
        res22, res12 = decoder(model, feat2, feat1, pos2, pos1, shape_j[b], shape_i[b])
        res = [res11, res21, res22, res12]
        Xb, Cb, Db, Qb = zip(
            *[
                (r["pts3d"][0], r["conf"][0], r["desc"][0], r["desc_conf"][0])
                for r in res
            ]
        )
        X.append(torch.stack(Xb, dim=0))
        C.append(torch.stack(Cb, dim=0))
        D.append(torch.stack(Db, dim=0))
        Q.append(torch.stack(Qb, dim=0))

    X, C, D, Q = (
        torch.stack(X, dim=1),
        torch.stack(C, dim=1),
        torch.stack(D, dim=1),
        torch.stack(Q, dim=1),
    )
    X, C, D, Q = downsample(X, C, D, Q)
    return X, C, D, Q


@torch.inference_mode
def mast3r_inference_mono(model, frame):
    if frame.feat is None:
        frame.feat, frame.pos, _ = model._encode_image(frame.img, frame.img_true_shape)

    feat = frame.feat
    pos = frame.pos
    shape = frame.img_true_shape

    res11, res21 = decoder(model, feat, feat, pos, pos, shape, shape)
    res = [res11, res21]
    X, C, D, Q = zip(
        *[(r["pts3d"][0], r["conf"][0], r["desc"][0], r["desc_conf"][0]) for r in res]
    )
    # 4xhxwxc
    X, C, D, Q = torch.stack(X), torch.stack(C), torch.stack(D), torch.stack(Q)
    X, C, D, Q = downsample(X, C, D, Q)

    Xii, Xji = einops.rearrange(X, "b h w c -> b (h w) c")
    Cii, Cji = einops.rearrange(C, "b h w -> b (h w) 1")

    return Xii, Cii


def mast3r_match_symmetric(model, feat_i, pos_i, feat_j, pos_j, shape_i, shape_j):
    X, C, D, Q = mast3r_decode_symmetric_batch(
        model, feat_i, pos_i, feat_j, pos_j, shape_i, shape_j
    )

    # Ordering 4xbxhxwxc
    b = X.shape[1]

    Xii, Xji, Xjj, Xij = X[0], X[1], X[2], X[3]
    Dii, Dji, Djj, Dij = D[0], D[1], D[2], D[3]
    Qii, Qji, Qjj, Qij = Q[0], Q[1], Q[2], Q[3]

    # Always matching both
    X11 = torch.cat((Xii, Xjj), dim=0)
    X21 = torch.cat((Xji, Xij), dim=0)
    D11 = torch.cat((Dii, Djj), dim=0)
    D21 = torch.cat((Dji, Dij), dim=0)

    # tic()
    idx_1_to_2, valid_match_2 = matching.match(X11, X21, D11, D21)
    # toc("Match")

    # TODO: Avoid this
    match_b = X11.shape[0] // 2
    idx_i2j = idx_1_to_2[:match_b]
    idx_j2i = idx_1_to_2[match_b:]
    valid_match_j = valid_match_2[:match_b]
    valid_match_i = valid_match_2[match_b:]

    return (
        idx_i2j,
        idx_j2i,
        valid_match_j,
        valid_match_i,
        Qii.view(b, -1, 1),
        Qjj.view(b, -1, 1),
        Qji.view(b, -1, 1),
        Qij.view(b, -1, 1),
    )


@torch.inference_mode
def mast3r_asymmetric_inference(model, frame_i, frame_j):
    if frame_i.feat is None:
        frame_i.feat, frame_i.pos, _ = model._encode_image(
            frame_i.img, frame_i.img_true_shape
        )
    if frame_j.feat is None:
        frame_j.feat, frame_j.pos, _ = model._encode_image(
            frame_j.img, frame_j.img_true_shape
        )

    feat1, feat2 = frame_i.feat, frame_j.feat
    pos1, pos2 = frame_i.pos, frame_j.pos
    shape1, shape2 = frame_i.img_true_shape, frame_j.img_true_shape

    res11, res21 = decoder(model, feat1, feat2, pos1, pos2, shape1, shape2)
    res = [res11, res21]
    X, C, D, Q = zip(
        *[(r["pts3d"][0], r["conf"][0], r["desc"][0], r["desc_conf"][0]) for r in res]
    )
    # 4xhxwxc
    X, C, D, Q = torch.stack(X), torch.stack(C), torch.stack(D), torch.stack(Q)
    X, C, D, Q = downsample(X, C, D, Q)
    return X, C, D, Q


def mast3r_match_asymmetric(model, frame_i, frame_j, idx_i2j_init=None):
    X, C, D, Q = mast3r_asymmetric_inference(model, frame_i, frame_j)

    b, h, w = X.shape[:-1]
    # 2 outputs per inference
    b = b // 2

    Xii, Xji = X[:b], X[b:]
    Cii, Cji = C[:b], C[b:]
    Dii, Dji = D[:b], D[b:]
    Qii, Qji = Q[:b], Q[b:]

    idx_i2j, valid_match_j = matching.match(
        Xii, Xji, Dii, Dji, idx_1_to_2_init=idx_i2j_init
    )

    # How rest of system expects it
    Xii, Xji = einops.rearrange(X, "b h w c -> b (h w) c")
    Cii, Cji = einops.rearrange(C, "b h w -> b (h w) 1")
    Dii, Dji = einops.rearrange(D, "b h w c -> b (h w) c")
    Qii, Qji = einops.rearrange(Q, "b h w -> b (h w) 1")

    return idx_i2j, valid_match_j, Xii, Cii, Qii, Xji, Cji, Qji


def mast3r_estimate(model, img1: str, img2: str, device='cuda', cache_dir='./temp_cache'):
    img_paths = [img1, img2]
    image_size = 512
    optim_level = 'refine+depth'
    matching_conf_thr = 5.0
    lr1 = 0.07
    niter1 = 100
    lr2 = 0.014
    niter2 = 100
    shared_intrinsics = False
    TSDF_thresh = 0.0
    clean_depth = True

    # 加载图像
    # print("Loading images...", img_paths)
    imgs = load_images(img_paths, size=image_size, verbose=False)
    if len(imgs) == 1:
        imgs = [imgs[0], copy.deepcopy(imgs[0])]
        imgs[1]['idx'] = 1
        img_paths = [img_paths[0], img_paths[0] + '_2']

    # 构建图像对
    pairs = make_pairs(imgs, scene_graph='complete', prefilter=None, symmetrize=True)
    torch.set_grad_enabled(True)
    # 运行 MASt3R 稀疏全局对齐
    os.makedirs(cache_dir, exist_ok=True)
    scene = sparse_global_alignment(
        img_paths, pairs, cache_dir,
        model,
        device=device,
        lr1=lr1, niter1=niter1, lr2=lr2, niter2=niter2,
        opt_depth='depth' in optim_level,
        matching_conf_thr=matching_conf_thr,
        shared_intrinsics=shared_intrinsics
    )

    # 获取 pose 和 point cloud
    cams2world = to_numpy(scene.get_im_poses().cpu())  # shape (N, 4, 4)
    focals = to_numpy(scene.get_focals().cpu())        # shape (N, 2)
    pps = to_numpy(scene.get_principal_points().cpu())

    return cams2world, focals, pps


def dust3r_estimate(img, pts3d, conf, device='cpu'):
    """
    利用fast_pnp算法根据图像、3D点云和置信度数据估计相机位姿
    
    参数:
    img: numpy数组，形状为(H, W, 3)，原始图像
    pts3d: numpy数组或torch张量，形状为(H, W, 3)，3D点云
    conf: numpy数组或torch张量，形状为(H, W, 1)，置信度图
    device: str，计算设备 ('cpu' 或 'cuda')
    
    返回:
    best_focal: float，估计的焦距
    pose: numpy数组，形状为(4, 4)，相机到世界的变换矩阵
    """
    
    # 确保输入是numpy数组
    if torch.is_tensor(img):
        img = img.cpu().numpy()
    if torch.is_tensor(pts3d):
        pts3d = pts3d.cpu().numpy()
    if torch.is_tensor(conf):
        conf = conf.cpu().numpy()
    
    # 确保数据类型正确
    img = img.astype(np.float32) / 255.0 if img.max() > 1.0 else img.astype(np.float32)
    pts3d = pts3d.astype(np.float32)
    conf = conf.astype(np.float32)
    
    H, W, _ = img.shape
    
    # 创建像素网格
    pixels = np.mgrid[:W, :H].T.astype(np.float32)
    
    # 根据置信度创建掩码，选择置信度较高的点
    conf_threshold = np.quantile(conf, 0.8)  # 使用前20%的点
    msk = conf.squeeze() > conf_threshold
    
    # 确保至少有4个点
    if msk.sum() < 4:
        # 如果置信度阈值太高，降低阈值
        conf_threshold = np.quantile(conf, 0.5)
        msk = conf.squeeze() > conf_threshold
        if msk.sum() < 4:
            raise ValueError("Not enough points with sufficient confidence for PnP")
    
    # 如果没有提供初始焦距，使用启发式方法估计
    focal = max(W, H)  # 初始估计
    tentative_focals = [focal] if focal is not None else np.geomspace(max(W, H)/2, max(W, H)*3, 21)
    
    # 主点默认在图像中心
    pp = (W/2, H/2)
    
    best = (0, None, None, None)
    
    # 尝试不同的焦距估计
    for focal in tentative_focals:
        K = np.float32([(focal, 0, pp[0]), (0, focal, pp[1]), (0, 0, 1)])
        
        try:
            # 使用OpenCV的solvePnPRansac
            success, rvec, tvec, inliers = cv2.solvePnPRansac(
                pts3d[msk], 
                pixels[msk], 
                K, 
                None,
                iterationsCount=100, 
                reprojectionError=5, 
                flags=cv2.SOLVEPNP_SQPNP
            )
            
            if not success:
                continue
            
            score = len(inliers)
            if score > best[0]:
                best = (score, rvec, tvec, focal)
                
        except cv2.error:
            # OpenCV错误处理
            continue
    
    if not best[0]:
        raise RuntimeError("PnP算法未能找到解决方案")
    
    _, rvec, tvec, best_focal = best
    
    # 将旋转向量转换为旋转矩阵
    R_mat, _ = cv2.Rodrigues(rvec)
    
    # 构建4x4变换矩阵 (世界到相机)
    T_world_to_cam = np.eye(4, dtype=np.float32)
    T_world_to_cam[:3, :3] = R_mat
    T_world_to_cam[:3, 3] = tvec.squeeze()
    
    # 转换为相机到世界变换矩阵
    T_cam_to_world = np.linalg.inv(T_world_to_cam)
    
    return T_cam_to_world, float(best_focal), pp


def transpose_pose(base_pose, cam_pose, target_pose):
    """
    将target_pose从cam_pose坐标系转换到base_pose坐标系
    """
    target_pose_hat = base_pose @ np.linalg.inv(cam_pose) @ target_pose
    return target_pose_hat

def _resize_pil_image(img, long_edge_size):
    S = max(img.size)
    if S > long_edge_size:
        interp = PIL.Image.LANCZOS
    elif S <= long_edge_size:
        interp = PIL.Image.BICUBIC
    new_size = tuple(int(round(x * long_edge_size / S)) for x in img.size)
    return img.resize(new_size, interp)


def resize_img(img, size, square_ok=False, return_transformation=False):
    assert size == 224 or size == 512
    # numpy to PIL format
    img = PIL.Image.fromarray(np.uint8(img * 255))
    W1, H1 = img.size
    if size == 224:
        # resize short side to 224 (then crop)
        img = _resize_pil_image(img, round(size * max(W1 / H1, H1 / W1)))
    else:
        # resize long side to 512
        img = _resize_pil_image(img, size)
    W, H = img.size
    cx, cy = W // 2, H // 2
    if size == 224:
        half = min(cx, cy)
        img = img.crop((cx - half, cy - half, cx + half, cy + half))
    else:
        halfw, halfh = ((2 * cx) // 16) * 8, ((2 * cy) // 16) * 8
        if not (square_ok) and W == H:
            halfh = 3 * halfw / 4
        img = img.crop((cx - halfw, cy - halfh, cx + halfw, cy + halfh))

    res = dict(
        img=ImgNorm(img)[None],
        true_shape=np.int32([img.size[::-1]]),
        unnormalized_img=np.asarray(img),
    )
    if return_transformation:
        scale_w = W1 / W
        scale_h = H1 / H
        half_crop_w = (W - img.size[0]) / 2
        half_crop_h = (H - img.size[1]) / 2
        return res, (scale_w, scale_h, half_crop_w, half_crop_h)

    return res
